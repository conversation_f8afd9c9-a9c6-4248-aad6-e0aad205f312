import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { AdminLayout } from "@/components/admin-layout";
import { Save, ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Widget, widgetService, WidgetSettings } from "@/utils/widgetService";
import { Spinner } from "@/components/ui/spinner";
import { WidgetConfigModule } from "@/components/widget-config";
import { ChatWidgetPreview } from "@/components/widget-preview";
import { useWidgetSettings } from "@/hooks/use-widget-settings";
import api from "@/utils/api";

interface WidgetConfigPageProps {
  widgetId?: string;
}

const WidgetConfig = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [widget, setWidget] = useState<Widget | null>(null);

  const {
    widgets,
    selectedWidget,
    widgetSettings,
    setSelectedWidget,
    updateWidgetSettings,
    refreshWidgets
  } = useWidgetSettings();

  // Load widget if editing an existing one
  useEffect(() => {
    if (id) {
      const fetchWidget = async () => {
        setIsLoading(true);
        try {
          const response = await widgetService.getWidget(parseInt(id));
          const widgetData = response.data;
          setWidget(widgetData);

          // Set the selected widget in the hook
          if (widgetData.widget_id) {
            setSelectedWidget(widgetData.widget_id);
          }
        } catch (error) {
          console.error("Failed to fetch widget:", error);
          toast({
            title: "Error",
            description: "Failed to load widget data. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      };

      fetchWidget();
    }
  }, [id, setSelectedWidget, toast]);

  // Handle settings change
  const handleSettingsChange = async (newSettings: Partial<WidgetSettings>) => {
    try {
      await updateWidgetSettings(newSettings);
      toast({
        title: "Success",
        description: "Widget settings updated successfully",
      });
    } catch (error) {
      console.error("Failed to save widget settings:", error);
      toast({
        title: "Error",
        description: "Failed to save widget settings. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate('/dashboard/widgets');
  };

  return (
    <AdminLayout>
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Widgets
            </Button>
            <div>
              <h1 className="text-2xl font-bold">
                {id ? "Edit Widget" : "Widget Configuration"}
              </h1>
              {widget && (
                <p className="text-muted-foreground">
                  Editing: {widget.name}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Spinner className="h-8 w-8" />
          </div>
        ) : (
          /* Widget Configuration */
          <div className="grid gap-6">
            {id && selectedWidget ? (
              /* Edit Mode - Use the widget config module */
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <WidgetConfigModule />
                </div>
                <div className="flex justify-center items-start">
                  <div className="sticky top-6">
                    <h2 className="text-lg font-semibold mb-4">Live Preview</h2>
                    <ChatWidgetPreview settings={widgetSettings} />
                  </div>
                </div>
              </div>
            ) : (
              /* Create Mode or No Widget Selected */
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  {id ? "Loading widget..." : "Please select a widget to configure"}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default WidgetConfig;
