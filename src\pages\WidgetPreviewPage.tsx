import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { AdminLayout } from "@/components/admin-layout";
import { ChatWidgetPreview } from "@/components/widget-preview";
import { widgetService, Widget } from "@/utils/widgetService";
import { apiService } from "@/utils/api-service";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Loader2, Settings, Bot, Palette, Zap } from "lucide-react";

const WidgetPreviewPage = () => {
    const [widget, setWidget] = useState<Widget | null>(null);
    const [aiModel, setAiModel] = useState<any>(null);
    const [promptTemplate, setPromptTemplate] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const fetchWidget = async () => {
            setLoading(true);
            setError(null);
            try {
                // Get the widget ID from URL query params
                const params = new URLSearchParams(location.search);
                const widgetId = params.get("id");

                if (!widgetId) {
                    setError("No widget ID provided");
                    setLoading(false);
                    return;
                }

                const response = await widgetService.getWidget(Number(widgetId));
                const fetchedWidget = response.data;

                console.log('Fetched widget data:', fetchedWidget); // Debug log

                setWidget(fetchedWidget);

                // AI model should be included in the widget response via relationship
                if (fetchedWidget.ai_model) {
                    console.log('AI model found:', fetchedWidget.ai_model); // Debug log
                    setAiModel(fetchedWidget.ai_model);
                } else if (fetchedWidget.ai_model_id) {
                    console.log('AI model ID found, fetching separately:', fetchedWidget.ai_model_id); // Debug log
                    // Fallback: fetch AI model separately if not included in response
                    try {
                        const aiModelResponse = await apiService.get(`ai-models/${fetchedWidget.ai_model_id}`);
                        setAiModel(aiModelResponse.data);
                    } catch (err) {
                        console.warn('Failed to fetch AI model data:', err);
                    }
                }

                // Fetch prompt template data if AI model has one
                if (fetchedWidget.ai_model?.template_id) {
                    console.log('Template ID found:', fetchedWidget.ai_model.template_id); // Debug log
                    try {
                        const templateResponse = await apiService.get(`templates/${fetchedWidget.ai_model.template_id}`);
                        setPromptTemplate(templateResponse.data);
                    } catch (err) {
                        console.warn('Failed to fetch prompt template data:', err);
                    }
                }
            } catch (err) {
                console.error("Error fetching widget:", err);
                setError("Failed to load widget. Please try again.");
            } finally {
                setLoading(false);
            }
        };

        fetchWidget();
    }, [location.search]);

    const handleBack = () => {
        navigate("/dashboard/widgets");
    };

    return (
        <AdminLayout>
            <div className="flex flex-col h-full space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button variant="outline" size="sm" onClick={handleBack}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Widgets
                        </Button>
                        <h1 className="text-2xl font-bold tracking-tight">
                            {widget ? `Preview: ${widget.name}` : "Widget Preview"}
                        </h1>
                    </div>
                </div>

                <div className="flex-1 bg-muted rounded-lg p-6 flex items-center justify-center">
                    {loading ? (
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <Loader2 className="h-10 w-10 animate-spin mb-4" />
                            <p>Loading widget preview...</p>
                        </div>
                    ) : error ? (
                        <div className="text-center text-destructive">
                            <p>{error}</p>
                            <Button variant="outline" className="mt-4" onClick={handleBack}>
                                Return to Widgets List
                            </Button>
                        </div>
                    ) : widget ? (
                        <div className="w-full max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* Widget Information Panel */}
                            <div className="lg:col-span-1 space-y-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Settings className="h-5 w-5" />
                                            Widget Information
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <h3 className="font-semibold text-lg">{widget.name}</h3>
                                            <p className="text-sm text-muted-foreground">ID: {widget.widget_id || widget.id}</p>
                                            <Badge variant={widget.is_active ? "default" : "secondary"} className="mt-1">
                                                {widget.is_active ? "Active" : "Inactive"}
                                            </Badge>
                                        </div>

                                        {aiModel && (
                                            <div className="border-t pt-4">
                                                <h4 className="font-medium flex items-center gap-2 mb-2">
                                                    <Bot className="h-4 w-4" />
                                                    AI Model
                                                </h4>
                                                <p className="text-sm">{aiModel.name}</p>
                                                <p className="text-xs text-muted-foreground">{aiModel.provider}</p>
                                                {aiModel.description && (
                                                    <p className="text-xs text-muted-foreground mt-1">{aiModel.description}</p>
                                                )}
                                            </div>
                                        )}

                                        {promptTemplate && (
                                            <div className="border-t pt-4">
                                                <h4 className="font-medium flex items-center gap-2 mb-2">
                                                    <Zap className="h-4 w-4" />
                                                    Prompt Template
                                                </h4>
                                                <p className="text-sm">{promptTemplate.name}</p>
                                                <p className="text-xs text-muted-foreground">{promptTemplate.category}</p>
                                                {promptTemplate.description && (
                                                    <p className="text-xs text-muted-foreground mt-1">{promptTemplate.description}</p>
                                                )}
                                            </div>
                                        )}

                                        <div className="border-t pt-4">
                                            <h4 className="font-medium flex items-center gap-2 mb-2">
                                                <Palette className="h-4 w-4" />
                                                Settings
                                            </h4>
                                            <div className="space-y-2 text-sm">
                                                <p>Primary Color: {widget.settings?.primaryColor || '#4f46e5'}</p>
                                                <div className="flex items-center gap-2">
                                                    <div
                                                        className="w-4 h-4 rounded-full border"
                                                        style={{ backgroundColor: widget.settings?.primaryColor || '#4f46e5' }}
                                                    />
                                                    <span>{widget.settings?.primaryColor || '#4f46e5'}</span>
                                                </div>
                                                <p>Position: {widget.settings?.position?.replace('-', ' ') || 'bottom-right'}</p>
                                                <p>Bot Name: {widget.settings?.headerTitle || 'AI Assistant'}</p>
                                                <p>Welcome Message: {widget.settings?.welcomeMessage || 'Hello! How can I help you today?'}</p>
                                                <p>Pre-Chat Form: {widget.settings?.preChat ? 'Enabled' : 'Disabled'}</p>
                                                <p>Required Info: {widget.settings?.requireGuestInfo ? 'Yes' : 'No'}</p>
                                                <p>Post-Chat Survey: {widget.settings?.postChat ? 'Enabled' : 'Disabled'}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Widget Preview */}
                            <div className="lg:col-span-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Live Preview</CardTitle>
                                        <p className="text-sm text-muted-foreground">
                                            This preview shows exactly how your widget will behave with the current settings.
                                        </p>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="bg-gray-100 rounded-lg p-6 min-h-[600px] flex items-center justify-center">
                                            <ChatWidgetPreview
                                                settings={widget.settings || {}}
                                                widgetId={widget.widget_id || widget.id?.toString()}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center text-muted-foreground">
                            <p>Widget not found</p>
                            <Button variant="outline" className="mt-4" onClick={handleBack}>
                                Return to Widgets List
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
};

export default WidgetPreviewPage;